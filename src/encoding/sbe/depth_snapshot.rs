#[derive(Debug)]
pub struct SbeDepthSnapshot<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub last_update_id: u64,
    pub bids: Vec<(f64, f64)>,
    pub asks: Vec<(f64, f64)>,
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if exponent >= 0 {
        mantissa as f64 * 10_f64.powi(exponent as i32)
    } else {
        mantissa as f64 / 10_f64.powi((-exponent) as i32)
    }
}

pub fn parse_sbe_depth_snapshot(data: &[u8]) -> Option<SbeDepthSnapshot<'_>> {
    let data = &data[8..];

    let event_time = u64::from_le_bytes(data[..8].try_into().unwrap());
    let last_update_id = u64::from_le_bytes(data[8..16].try_into().unwrap());
    let price_exponent = data[16] as i8;
    let qty_exponent = data[17] as i8;
    // let block_len_bids = u16::from_le_bytes([data[18], data[19]]);
    let bids_count = u16::from_le_bytes([data[20], data[21]]);

    let mut bids = Vec::with_capacity(bids_count as usize);
    let mut offset = 22;

    for _ in 0..bids_count {
        if offset + 16 > data.len() {
            return None;
        }
        let price_mantissa = i64::from_le_bytes(data[offset..offset + 8].try_into().unwrap());
        let qty_mantissa = i64::from_le_bytes(data[offset + 8..offset + 16].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);
        bids.push((price, qty));
        offset += 16;
    }

    // let block_len_asks = u16::from_le_bytes([data[offset], data[offset + 1]]);
    let asks_count = u16::from_le_bytes([data[offset + 2], data[offset + 3]]);
    offset += 4;
    let mut asks = Vec::with_capacity(asks_count as usize);
    for _ in 0..asks_count {
        if offset + 16 > data.len() {
            return None;
        }

        let price_mantissa = i64::from_le_bytes(data[offset..offset + 8].try_into().unwrap());
        let qty_mantissa = i64::from_le_bytes(data[offset + 8..offset + 16].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);

        asks.push((price, qty));
        offset += 16;
    }

    // 解析symbol字段
    let symbol = if offset < data.len() {
        let symbol_length = data[offset] as usize;
        if offset + 1 + symbol_length <= data.len() {
            match std::str::from_utf8(&data[offset + 1..offset + 1 + symbol_length]) {
                Ok(s) => s,
                Err(_) => "UNKNOWN",
            }
        } else {
            "UNKNOWN"
        }
    } else {
        "UNKNOWN"
    };

    Some(SbeDepthSnapshot {
        symbol,
        event_time,
        last_update_id,
        bids,
        asks,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_sbe_depth_snapshot() {
        // 创建测试数据
        let mut data = Vec::new();

        // SBE头部 (8字节)
        data.extend_from_slice(&[0u8; 8]);

        // eventTime (8字节)
        data.extend_from_slice(&1234567890123456u64.to_le_bytes());

        // lastUpdateId (8字节)
        data.extend_from_slice(&987654321u64.to_le_bytes());

        // priceExponent (-8)
        data.push((-8i8) as u8);

        // qtyExponent (-8)
        data.push((-8i8) as u8);

        // bidsCount (1)
        data.push(1u8);

        // asksCount (1)
        data.push(1u8);

        // bid: price=5000000000000 (50000.0), qty=100000000 (1.0)
        data.extend_from_slice(&5000000000000i64.to_le_bytes());
        data.extend_from_slice(&100000000i64.to_le_bytes());

        // ask: price=5000100000000 (50001.0), qty=200000000 (2.0)
        data.extend_from_slice(&5000100000000i64.to_le_bytes());
        data.extend_from_slice(&200000000i64.to_le_bytes());

        // symbol: "BTCUSDT"
        data.push(7u8); // 长度
        data.extend_from_slice(b"BTCUSDT");

        let result = parse_sbe_depth_snapshot(&data).unwrap();

        assert_eq!(result.symbol, "BTCUSDT");
        assert_eq!(result.event_time, 1234567890123456);
        assert_eq!(result.last_update_id, 987654321);
        assert_eq!(result.bids.len(), 1);
        assert_eq!(result.asks.len(), 1);
        assert_eq!(result.bids[0].price, 50000.0);
        assert_eq!(result.bids[0].qty, 1.0);
        assert_eq!(result.asks[0].price, 50001.0);
        assert_eq!(result.asks[0].qty, 2.0);
    }
}
